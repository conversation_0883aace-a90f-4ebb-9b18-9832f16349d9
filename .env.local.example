###############################################################################
# Required Configuration
# These settings are essential for the basic functionality of the system.
###############################################################################

# OpenAI API key retrieved here: https://platform.openai.com/api-keys
OPENAI_API_KEY=[YOUR_OPENAI_API_KEY]

# Search Configuration
TAVILY_API_KEY=[YOUR_TAVILY_API_KEY]  # Get your API key at: https://app.tavily.com/home

# Optional Docker Configuration (only needed in some Docker environments)
# BASE_URL=http://localhost:3000  # Use only if models.j<PERSON> fails to load in Docker

###############################################################################
# Optional Features
# Enable these features by uncommenting and configuring the settings below
###############################################################################

#------------------------------------------------------------------------------
# Chat History Storage
# Enable persistent chat history across sessions
#------------------------------------------------------------------------------
# ENABLE_SAVE_CHAT_HISTORY=true  # enable chat history storage

# Redis Configuration (Required if ENABLE_SAVE_CHAT_HISTORY=true)
# Choose between local Redis or Upstash Redis
# OPTION 1: Local Redis
# USE_LOCAL_REDIS=false  # use local Redis
# LOCAL_REDIS_URL=redis://localhost:6379  # local Redis URL (if USE_LOCAL_REDIS=true)

# OPTION 2: Upstash Redis (Recommended for production)
# UPSTASH_REDIS_REST_URL=[YOUR_UPSTASH_REDIS_REST_URL]  # Upstash Redis REST URL (if USE_LOCAL_REDIS=false)
# UPSTASH_REDIS_REST_TOKEN=[YOUR_UPSTASH_REDIS_REST_TOKEN]  # Upstash Redis REST Token (if USE_LOCAL_REDIS=false)

#------------------------------------------------------------------------------
# Additional AI Providers
# Enable alternative AI models by configuring these providers
#------------------------------------------------------------------------------
# Google Generative AI
# GOOGLE_GENERATIVE_AI_API_KEY=[YOUR_GOOGLE_GENERATIVE_AI_API_KEY]

# Anthropic (Claude)
# ANTHROPIC_API_KEY=[YOUR_ANTHROPIC_API_KEY]

# Groq
# GROQ_API_KEY=[YOUR_GROQ_API_KEY]

# Ollama
# OLLAMA_BASE_URL=http://localhost:11434

# Azure OpenAI
# AZURE_API_KEY=
# AZURE_RESOURCE_NAME=

# DeepSeek
# DEEPSEEK_API_KEY=[YOUR_DEEPSEEK_API_KEY]

# Fireworks
# FIREWORKS_API_KEY=[YOUR_FIREWORKS_API_KEY]

# xAI (Grok)
# XAI_API_KEY=[YOUR_XAI_API_KEY]

# OpenAI Compatible Model
# OPENAI_COMPATIBLE_API_KEY=
# OPENAI_COMPATIBLE_API_BASE_URL=

#------------------------------------------------------------------------------
# Alternative Search Providers
# Configure different search backends (default: Tavily)
#------------------------------------------------------------------------------
# SEARCH_API=searxng  # Available options: tavily, searxng, exa

# SearXNG Configuration (Required if SEARCH_API=searxng)
# SEARXNG_API_URL=http://localhost:8080  # Replace with your local SearXNG API URL or docker http://searxng:8080
# SEARXNG_SECRET=""  # generate a secret key e.g. openssl rand -base64 32
# SEARXNG_PORT=8080
# SEARXNG_BIND_ADDRESS=0.0.0.0
# SEARXNG_IMAGE_PROXY=true
# SEARXNG_LIMITER=false
# SEARXNG_DEFAULT_DEPTH=basic
# SEARXNG_MAX_RESULTS=50
# SEARXNG_ENGINES=google,bing,duckduckgo,wikipedia
# SEARXNG_TIME_RANGE=None
# SEARXNG_SAFESEARCH=0

#------------------------------------------------------------------------------
# Additional Features
# Enable extra functionality as needed
#------------------------------------------------------------------------------
# NEXT_PUBLIC_ENABLE_SHARE=true  # Enable sharing of chat conversations
# SERPER_API_KEY=[YOUR_SERPER_API_KEY]  # Enable video search capability
# JINA_API_KEY=[YOUR_JINA_API_KEY]  # Alternative to Tavily for retrieve tool


# ------------------------------
# Supabase Configuration (Auth)
# ------------------------------
# Your Supabase project URL.
# NEXT_PUBLIC_SUPABASE_URL=[YOUR_NEXT_PUBLIC_SUPABASE_URL]

# Your Supabase project's anonymous (public) key.
# NEXT_PUBLIC_SUPABASE_ANON_KEY=[YOUR_NEXT_PUBLIC_SUPABASE_ANON_KEY]