{"name": "morphic", "version": "0.1.0", "private": true, "license": "Apache-2.0", "scripts": {"dev": "next dev --turbo -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/azure": "^1.1.5", "@ai-sdk/deepseek": "^0.1.6", "@ai-sdk/fireworks": "^0.1.6", "@ai-sdk/google": "^1.1.5", "@ai-sdk/groq": "^1.1.6", "@ai-sdk/openai": "^1.3.12", "@ai-sdk/xai": "^1.1.10", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-tooltip": "^1.2.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/typography": "^0.5.12", "@upstash/redis": "^1.34.0", "@vercel/analytics": "^1.5.0", "ai": "^4.3.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "1.0.0", "embla-carousel-react": "^8.0.0", "exa-js": "^1.0.12", "jsdom": "^22.1.0", "katex": "^0.16.10", "lucide-react": "^0.507.0", "next": "^15.2.3", "next-themes": "^0.3.0", "node-html-parser": "^6.1.13", "ollama-ai-provider": "^1.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.0.1", "react-markdown": "^8.0.7", "react-resizable-panels": "^3.0.0", "react-syntax-highlighter": "^15.5.0", "react-textarea-autosize": "^8.5.3", "redis": "^4.7.0", "rehype-external-links": "^3.0.0", "rehype-katex": "^6.0.0", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "sonner": "^1.4.41", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@types/jsdom": "^21.1.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^8", "eslint-config-next": "^14.2.25", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "engines": {"bun": "1.2.12"}}