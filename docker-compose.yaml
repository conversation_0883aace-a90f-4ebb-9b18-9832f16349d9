# Docker Compose configuration for the morphic-stack development environment

name: morphic-stack
services:
  morphic:
    image: ghcr.io/${GITHUB_REPOSITORY:-your-username/morphic}:latest
    command: bun start -H 0.0.0.0 -p 3001
    build:
      context: .
      dockerfile: Dockerfile
      cache_from:
        - morphic:builder
        - morphic:latest
    env_file: .env.local # Load environment variables
    ports:
      - '3001:3001' # Maps port 3001 on the host to port 3001 in the container.
    depends_on:
      - redis
      - searxng

  redis:
    image: redis:alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  searxng:
    image: searxng/searxng
    ports:
      - '${SEARXNG_PORT:-8080}:8080'
    env_file: .env.local # can remove if you want to use env variables or in settings.yml
    volumes:
      - ./searxng-limiter.toml:/etc/searxng/limiter.toml
      - ./searxng-settings.yml:/etc/searxng/settings.yml
      - searxng_data:/data

volumes:
  redis_data:
  searxng_data:
