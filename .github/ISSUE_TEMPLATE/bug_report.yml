name: 🐞 Bug
description: File a bug/issue
title: '[BUG] <title>'
labels: ['Bug', 'Needs Triage']
body:
  - type: checkboxes
    attributes:
      label: Is there an existing issue for this?
      description: Please search to see if an issue already exists for the bug you encountered.
      options:
        - label: I have searched the existing issues
          required: true
  - type: checkboxes
    attributes:
      label: Vercel Runtime Logs
      description: If this is a Vercel environment issue, have you checked the Vercel Runtime Logs? (https://vercel.com/docs/observability/runtime-logs)
      options:
        - label: I have checked the Vercel Runtime Logs for errors (if applicable)
          required: false
  - type: textarea
    attributes:
      label: Current Behavior
      description: A concise description of what you're experiencing.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
      description: A concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        1. In this environment...
        2. With this config...
        3. Run '...'
        4. See error...
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment
      description: |
        examples:
          - Browser: Chrome 52.0.2743.116
      value: |
        - OS:
        - Browser:
      render: markdown
    validations:
      required: true
  - type: textarea
    attributes:
      label: Anything else?
      description: |
        Links? References? Anything that will give us more context about the issue you are encountering!

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: false
