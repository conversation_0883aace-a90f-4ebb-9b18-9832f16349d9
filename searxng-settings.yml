use_default_settings: true
server:
  # Is overwritten by ${SEARXNG_PORT} and ${SEARXNG_BIND_ADDRESS}
  port: 8888
  bind_address: '0.0.0.0'
  # public URL of the instance, to ensure correct inbound links. Is overwritten
  # by ${SEARXNG_URL}.
  base_url: false # "http://example.com/location"
  # rate limit the number of request on the instance, block some bots.
  # Is overwritten by ${SEARXNG_LIMITER}
  limiter: false
  # enable features designed only for public instances.
  # Is overwritten by ${SEARXNG_PUBLIC_INSTANCE}
  public_instance: false

  # If your instance owns a /etc/searxng/settings.yml file, then set the following
  # values there.

  secret_key: 'ursecretkey' # Is overwritten by ${SEARXNG_SECRET}
  # Proxy image results through SearXNG. Is overwritten by ${SEARXNG_IMAGE_PROXY}
  image_proxy: false
  # 1.0 and 1.1 are supported
  http_protocol_version: '1.0'
  # POST queries are more secure as they don't show up in history but may cause
  # problems when using Firefox containers
  method: 'POST'
  default_http_headers:
    X-Content-Type-Options: nosniff
    X-Download-Options: noopen
    X-Robots-Tag: noindex, nofollow
    Referrer-Policy: no-referrer

search:
  formats:
    - json  