# Contributing to Morphic

Thank you for your interest in contributing to Morphic! This document provides guidelines and instructions for contributing.

## Code of Conduct

By participating in this project, you are expected to uphold our [Code of Conduct](CODE_OF_CONDUCT.md).

## How to Contribute

### Reporting Issues

- Check if the issue already exists in our [GitHub Issues](https://github.com/miurla/morphic/issues)
- Use the issue templates when creating a new issue
- Provide as much context as possible

### Pull Requests

1. Fork the repository
2. Create a new branch from `main`:
   ```bash
   git checkout -b feat/your-feature-name
   ```
3. Make your changes
4. Commit your changes using conventional commits:
   ```bash
   git commit -m "feat: add new feature"
   ```
5. Push to your fork
6. Open a Pull Request

### Commit Convention

We use conventional commits. Examples:

- `feat: add new feature`
- `fix: resolve issue with X`
- `docs: update README`
- `chore: update dependencies`
- `refactor: improve code structure`

### Development Setup

Follow the [Quickstart](README.md#-quickstart) guide in the README to set up your development environment.

## License

By contributing, you agree that your contributions will be licensed under the Apache-2.0 License.
